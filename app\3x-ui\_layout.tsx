import { Label } from '@/components/ui/label';
import { useThemeColor } from '@/hooks/useThemeColor';
import { Stack, router } from 'expo-router';
import React from 'react';

export default function ThreeXUILayout() {
  const backgroundColor = useThemeColor({}, 'background');
  const textColor = useThemeColor({}, 'text');

  const handleCloseModal = () => {
    router.back();
  };

  return (
    <Stack
      screenOptions={{
        headerTintColor: textColor,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      }}
    >
      
      {/* 入站配置模态框 */}
      <Stack.Screen
        name="inbound-config"
        options={({ route }) => ({
          presentation:'modal',
          title: (route.params as any)?.id ? '编辑入站配置' : '添加入站配置',
          headerShown: true,
          headerLeft: () => (
            <Label onPress={handleCloseModal} style={{ color: textColor }}>
              取消
            </Label>
          ),
          
        })}
      />

      {/* 出站配置模态框 */}
      <Stack.Screen
        name="outbound-config"
        options={({ route }) => ({
          presentation:'modal',
          title: (route.params as any)?.index !== undefined ? '编辑出站配置' : '添加出站配置',
          headerShown: true,
          headerLeft: () => (
            <Label onPress={handleCloseModal} style={{ color: textColor }}>
              取消
            </Label>
          ),
        })}
      />

      {/* 配置表单模态框 */}
      <Stack.Screen
        name="config-form"
        options={{
          presentation: 'modal',
          headerShown: true,
        }}
      />
    </Stack>
  );
}

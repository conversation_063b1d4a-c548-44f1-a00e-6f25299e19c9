import React from "react";
import { View, StyleSheet } from "react-native";
import { Skeleton } from "../../../components/ui/skeleton";
import { useThemeColor } from "~/hooks/useThemeColor";

export function InboundSkeletonCard() {
    const borderColor = useThemeColor({}, 'border');

    return (
        <View style={styles.cardContainer}>
            <View style={styles.card}>
                <View style={styles.header}>
                    {/* 标题骨架 */}
                    <Skeleton className="w-40 h-5 flex-1 mr-3" />
                    {/* 状态badge骨架 */}
                    <Skeleton className="w-12 h-6 rounded-xl" />
                </View>
            </View>
            <View style={[styles.divider, { backgroundColor: borderColor }]} />
        </View>
    );
}

const styles = StyleSheet.create({
    cardContainer: {
        // 不需要额外的margin，由padding控制间距
    },
    card: {
        paddingVertical: 12,
        paddingHorizontal: 12,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    divider: {
        height: 1,
    },
});

import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack, useRouter } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';

import '../global.css';

import { useColorScheme } from '@/hooks/useColorScheme';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import React from 'react';
import { Label } from '~/components/ui/label';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { PortalHost } from '@rn-primitives/portal';
import { Platform } from 'react-native';
import { FullWindowOverlay } from 'react-native-screens';

const WindowOverlay = Platform.OS === "ios" ? FullWindowOverlay : React.Fragment as any
// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const { loadData, isLoading } = useAppStore();
  const { t } = useTranslation();
  const router = useRouter()

  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  const handleCloseModel = () => {
    router.back();
  };

  useEffect(() => {
    // 加载应用数据
    loadData();
  }, [loadData]);

  useEffect(() => {
    if (loaded && !isLoading) {
      SplashScreen.hideAsync();
    }
  }, [loaded, isLoading]);

  if (!loaded || isLoading) {
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <GestureHandlerRootView>
        <BottomSheetModalProvider>
      <Stack screenOptions={{headerShown:false}}>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen
          name="add-config"
          options={{
            presentation: 'modal',
            title: t('navigation.addConfig'),
            headerShown: true,
            headerLeft: () => <Label onPress={handleCloseModel}>{t('common.cancel')}</Label>,
          }}
        />
        <Stack.Screen
          name="edit-groups"
          options={{
            presentation: 'modal',
            title: t('groups.title'),
            headerShown: true,
            headerLeft: () => <Label onPress={handleCloseModel}>{t('common.cancel')}</Label>,
          }}
        />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
      </BottomSheetModalProvider>
      </GestureHandlerRootView>
      <WindowOverlay>
          <PortalHost />
      </WindowOverlay>
    </ThemeProvider>
  );
}
